# Environment Configuration Example
# Copy this file to .env and update the values

# Server Configuration
PORT=3000
NODE_ENV=development

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# Database Configuration (Neon PostgreSQL)
# Get this from your Neon dashboard: https://console.neon.tech/
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require

# Alternative individual database settings (if not using DATABASE_URL)
DB_HOST=ep-example-123456.us-east-1.aws.neon.tech
DB_PORT=5432
DB_NAME=neondb
DB_USER=username
DB_PASSWORD=password
DB_SSL=true

# Firebase Configuration (for Google Authentication)
# Get these values from your Firebase project console
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----<PERSON><PERSON>IN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"

# API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
